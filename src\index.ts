import { Server } from "./server";
import { normalizePort, onError } from "./serverHandler";
import http from "http";
import fs from "fs";
import { CONFIG } from "./config/environment";
import logger from "./util/logger";

const SERVER = new Server();

const PORT =   normalizePort(process.env.PORT || 3000);

SERVER.app.set("post", PORT);

let server = http.createServer(SERVER.app);

server.listen(PORT);

// server handler
server.on("error", error => onError(error, PORT));


server.on("listening", () => {
    const addr: any = server.address();
    const bind: string = (typeof addr === 'string') ? `pipe ${addr}` : `port ${addr.port}`;

    logger.info(`Listening on ${bind}`);    
});

if (!fs.existsSync(CONFIG.uploadsFolderPath)) {
    fs.mkdir(CONFIG.uploadsFolderPath, () => {
        console.log('Uploads folder created');

    })
} else {
    console.log('Uploads folders exists');

}

if (!fs.existsSync(CONFIG.cacheFolderPath)) {
    fs.mkdir(CONFIG.cacheFolderPath, () => {
        console.log('Cache folder created');

    })
} else {
    console.log('Cache folders exists');

}
