// src/utils/logger.ts
import { createLogger, format, transports } from "winston";
import path from "path";

const { combine, timestamp, printf, errors, colorize } = format;

// Custom log format
const logFormat = printf(({ level, message, timestamp, stack }) => {
  return `${timestamp} [${level}] : ${stack || message}`;
});

// Create logger
const logger = createLogger({
  level: "info", // default log level
  format: combine(
    timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    errors({ stack: true }), // capture stack trace for errors
    logFormat
  ),
  transports: [
    // Console logs (with colors)
    new transports.Console({
      format: combine(colorize({all:true}), logFormat),
    }),

    // File for all logs
    new transports.File({
      filename: path.join(__dirname, "../../logs/app.log"),
      level: "info",
    }),

    // File for error logs only
    new transports.File({
      filename: path.join(__dirname, "../../logs/error.log"),
      level: "error",
    }),
  ],
});

export default logger;
