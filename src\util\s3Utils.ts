import { s3Client, onboardingS3UploadParams } from "../config/s3";

export interface S3UrlParseResult {
  bucket: string;
  key: string;
  region?: string;
}

export function parseS3Url(url: string): S3UrlParseResult | null {
  try {
    // Handle different S3 URL formats
    const urlObj = new URL(url);
    
    // Check if it's an S3 URL
    if (urlObj.hostname.includes('s3') || urlObj.hostname.includes('amazonaws.com')) {
      const pathParts = urlObj.pathname.split('/').filter(part => part);
      
      if (pathParts.length < 1) {
        return null;
      }
      
      // For S3 URLs, the bucket is in the hostname, not the path
      // Extract bucket from hostname: thoughtpudding-public.s3.ap-south-1.amazonaws.com
      const hostnameParts = urlObj.hostname.split('.');
      const bucket = hostnameParts[0]; // "thoughtpudding-public"
      
      // The key is the entire path
      const key = pathParts.join('/');
      
      return {
        bucket,
        key,
        region: hostnameParts[1] // Extract region from hostname
      };
    }
    
    // Handle custom domain URLs (like CDN URLs)
    // For blog images, they might be served through a CDN
    // We'll extract the path and assume it's the S3 key
    const pathParts = urlObj.pathname.split('/').filter(part => part);
    if (pathParts.length > 0) {
      return {
        bucket: onboardingS3UploadParams.Bucket,
        key: pathParts.join('/'),
        region: undefined
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing S3 URL:', error);
    return null;
  }
}

export async function deleteFileFromS3(key: string, bucket?: string): Promise<boolean> {
  try {
    const deleteParams = {
      Bucket: bucket || onboardingS3UploadParams.Bucket,
      Key: key
    };

    console.log('S3 Delete Params:', deleteParams);
    await s3Client.deleteObject(deleteParams).promise();
    return true;
  } catch (error) {
    console.error('Error deleting file from S3:', error);
    return false;
  }
}

export async function deleteMultipleFilesFromS3(keys: string[]): Promise<Array<{ key: string; success: boolean; error?: string }>> {
  const results = await Promise.all(
    keys.map(async (key) => {
      try {
        const success = await deleteFileFromS3(key);
        return { key, success };
      } catch (error: any) {
        return { key, success: false, error: error.message };
      }
    })
  );
  
  return results;
} 