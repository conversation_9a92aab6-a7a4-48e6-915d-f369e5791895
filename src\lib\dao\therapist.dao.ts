import moment from "moment";
import mongoose from "mongoose";
import { removeEmpty } from "../../helper/custom.helper";
import TherapistModel from "../../models/Therapist.model";

export class TherapistDao {
  static async createTherapist(payload: any) {
    return await TherapistModel.create(payload);
  }

  static async getTherapist(therapistId: any) {
    const condition = mongoose.Types.ObjectId.isValid(therapistId)
      ? { _id: therapistId }
      : { identifier: therapistId };
    return await TherapistModel.findOne(condition);
  }

  static async getLeanTherapist(therapistId: any) {
    return await TherapistModel.findOne(
      { _id: therapistId },
      "-password"
    ).lean();
  }

  static async updateTherapist(therapistId: any, therapistData: any) {
    return await TherapistModel.findOneAndUpdate(
      { _id: therapistId },
      {
        $set: therapistData,
      },
      { new: true }
    );
  }

  static async updateTherapistData(therapistId: any, therapistData: any) {
    return await TherapistModel.findByIdAndUpdate(
      therapistId,
      {
        $set: therapistData,
      },
      { new: true }
    );
  }

  static async deleteTherapist(therapistId: any) {
    return await TherapistModel.findOneAndDelete({ _id: therapistId });
  }

  static async getAll(
    pageSize: number,
    skip: number,
    therapistId: any,
    isVerified: any
  ) {
    const query = removeEmpty({ _id: therapistId, isVerified: isVerified });
    return await TherapistModel.find(query, "-password")
      .skip(skip)
      .limit(pageSize)
      .lean();
  }

  static async getAllCount(isVerified: any) {
    const query = removeEmpty({ isVerified: isVerified });
    return await TherapistModel.find(query, "-password").count();
  }

  static async getAllTherapists() {
    return await TherapistModel.find();
  }

  static async getAllActiveTherapists() {
    return await TherapistModel.find({ isVerified: true, isDeleted: false });
  }

  static async findByEmail(email: any) {
    return await TherapistModel.findOne({ email: email });
  }

  static async getTherapistData(therapistId: any) {
    return await TherapistModel.findById(
      { _id: therapistId },
      "email name image isVerified"
    );
  }

  static async getCount(days?: number | undefined) {
    let date = moment().subtract(10, "years").toDate();
    if (days) date = moment().subtract(days, "days").toDate();
    return await TherapistModel.countDocuments({ createdAt: { $gte: date } });
  }

  static async getApprovedCount() {
    return await TherapistModel.countDocuments({
      "bankDetails.upiApprove": true,
    });
  }

  static async updateSyncDate(therapistId: any, syncData: any) {
    return await TherapistModel.findByIdAndUpdate(
      { _id: therapistId },
      { $set: syncData },
      { new: true }
    );
  }

  static async getOnboardedTherapistData(therapistId: any) {
    return await TherapistModel.aggregate([
      { $match: { _id: new mongoose.Types.ObjectId(therapistId) } },
      {
        $project: {
          name: 1,
          pronouns: 1,
          gender: 1,
          minAge: 1,
          maxAge: 1,
          therapyTypes: 1,
          languages: 1,
          showFeeRange: 1,
          minFee: 1,
          maxFee: 1,
          location: 1,
          slotType: 1,
          timeZone: 1,
          professionalQualification: 1,
          values: 1,
          concerns: 1,
          practiceApproach: 1,
          profilePicUrl: 1,
          yearsOfExperience: "$verificationDetails.yearsOfExperience",
          designation: "$verificationDetails.practicingTitle",
          bookingURL: 1,
          bookingMessage: 1,
          fromPublicCalender: 1,
        },
      },
    ]);
  }
}
