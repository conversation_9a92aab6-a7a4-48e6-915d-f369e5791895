import moment from "moment-timezone";
import express from "express";

import { format } from "date-fns";
import { Utility } from "../util/util";
import { Mailer2 } from "../util/mailer2";
import { CONFIG } from "../config/environment";
import { isOverlap } from "../helper/custom.helper";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { ScheduleStatus } from "../models/Schedule.model";
import { ClientService } from "../services/client.service";
import RazorpayService from "../services/razorpay.service";
import { getErrorResponse } from "../helper/error.handler";
import { MailSubjectEnum } from "../lib/enum/subject.enum";
import { isOverlappingSlots } from "../helper/custom.helper";
import { ScheduleService } from "../services/schedule.service";
import { TherapistService } from "../services/therapist.service";
import { ICreateScheduleData } from "../services/schedule.service";
import { PayTrackerService } from "../services/payTracker.service";
import {
  PaymentTrackerTypeEnum,
  PaymentTrackerStatusEnum,
} from "../models/PayTracker.model";
import { validateTherapistTimeSlots } from "../helper/custom.helper";
import scheduleReminder from "../util/emailTemplate/scheduleReminder";
import { WorkingHoursService } from "../services/workingHours.service";
import { GoogleCalendarService } from "../services/googleCalendar.service";
import { sendWhatsAppNotification } from "../services/whatsAppNotification.service";
import introScheduleReminderForClient from "../util/emailTemplate/clientIntroScheduleReminder";
import sessionScheduleReminderForClient from "../util/emailTemplate/clientSessionScheduleReminder";
import introScheduleReminderForTherapist from "../util/emailTemplate/therapistIntroScheduleReminder";
import sessionScheduleReminderForTherapist from "../util/emailTemplate/therapistSessionScheduleReminder";

moment.tz.setDefault("Asia/Kolkata");

interface CalendarEvent {
  scheduleId?: any;
  scheduleRecId?: any;
  transactionId?: any;
  meetLink?: string;
  calenderEventId?: any;
  payTrackerId?: any;
  _id?: any;
  link?: any;
  start?: {
    dateTime?: any;
  };
}
interface GoogleCalendarEvent {
  link: string;
  calenderEventId: CalendarEvent[];
}

export class WorkingHoursController {
  static async setupWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const payload = req.body;

      // validating the data if already exists for the therapist
      const existingWorkingHours =
        await WorkingHoursService.getTherapistWorkingHours(therapistId);

      if (existingWorkingHours) {
        console.log("Working hours already exists for this therapist");
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Working hours already exists for this therapist"
            )
          );
      }

      const invalidPayload: any = [];
      Object.entries(payload).forEach(([key, value]) => {
        const invalidData = validateTherapistTimeSlots(key, value);
        if (invalidData && invalidData.length) {
          invalidPayload.push(...invalidData);
        }
      });

      if (invalidPayload.length > 0) {
        console.log("Overlapping slots found");
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Overlapping slots found",
              invalidPayload
            )
          );
      }

      // saving the working hours
      payload["therapistId"] = therapistId;
      const savedSlots = await WorkingHoursService.createWorkingHours(payload);

      if (!savedSlots) {
        console.log("Working hours setup error");
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Working hours setup error"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Working hours setup successfully",
        data: savedSlots,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async updateWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const payload = req.body;

      const invalidPayload: any = [];
      Object.entries(payload).forEach(([key, value]) => {
        const invalidData = validateTherapistTimeSlots(key, value);
        if (invalidData && invalidData.length) {
          invalidPayload.push(...invalidData);
        }
      });

      if (invalidPayload.length > 0) {
        console.log("Overlapping slots found");
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Overlapping slots found",
              invalidPayload
            )
          );
      }

      // updating the working hours
      const updatedSlots = await WorkingHoursService.updateWorkingHours(
        payload,
        therapistId
      );

      if (!updatedSlots) {
        console.log("Working hours update error");
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Working hours update error"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Working hours updated successfully",
        data: updatedSlots,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async getTherapistWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;

      const workingHoursData: any =
        await WorkingHoursService.getTherapistWorkingHours(therapistId);

      if (!workingHoursData) {
        console.log("No Therapist Working Hours Data Found");
        return res
          .status(404)
          .send(
            getErrorResponse(
              "NO_DATA_FOUND",
              "No Therapist Working Hours Data Found"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Fetched Therapist profile data successfully",
        data: workingHoursData,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async setupSpecificWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const payload = req.body;

      const invalidPayload: any = [];
      Object.entries(payload).forEach(([key, value]) => {
        const invalidData = validateTherapistTimeSlots(key, value);
        if (invalidData && invalidData.length) {
          invalidPayload.push(...invalidData);
        }
      });

      if (invalidPayload.length > 0) {
        console.log("Overlapping slots found");
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Overlapping slots found",
              invalidPayload
            )
          );
      }

      // First check if there are any existing slots for this date
      const existingWorkingHours: any =
        await WorkingHoursService.getSpecificWorkingHoursByDate(
          therapistId,
          payload.date
        );

      let isSlotsAvailable: any = false;

      if (existingWorkingHours) {
        isSlotsAvailable = true;
        const overlappingData: any = isOverlappingSlots(
          payload.slots,
          existingWorkingHours.slots
        );

        if (
          overlappingData.overlappingSlots &&
          overlappingData.overlappingSlots.length
        ) {
          console.log(
            "The requested slots overlap with existing slots for this date"
          );
          return res
            .status(400)
            .send(
              getErrorResponse(
                "VALIDATION_ERROR",
                "The requested slots overlap with existing slots for this date",
                overlappingData
              )
            );
        }
      }

      // checking in google calender if the slots are available
      // const isTimeSlotBusy: any = await GoogleCalendarService.isTimeSlotBusy(
      //   therapistId,
      //   payload.date,
      //   payload.slots
      // );

      // if (!isTimeSlotBusy.status) {
      //   console.log(isTimeSlotBusy.message);
      //   return res
      //     .status(400)
      //     .send(getErrorResponse("VALIDATION_ERROR", isTimeSlotBusy.message));
      // }

      // if (isTimeSlotBusy.busy) {
      //   console.log(isTimeSlotBusy.message);
      //   return res
      //     .status(400)
      //     .send(
      //       getErrorResponse(
      //         "VALIDATION_ERROR",
      //         isTimeSlotBusy.message,
      //         isTimeSlotBusy.busySlots
      //       )
      //     );
      // }

      // saving the working hours
      payload["therapistId"] = therapistId;
      const savedSlots = await WorkingHoursService.createSpecificWorkingHours(
        payload,
        isSlotsAvailable
      );

      if (!savedSlots) {
        console.log("Specific working hours setup error");
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Specific working hours setup error"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Specific working hours setup successfully",
        data: savedSlots,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async updateSpecificWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const workingHoursId = req.params.id;
      const payload = req.body;

      // verifying if the working hours exist
      const existingWorkingHours =
        await WorkingHoursService.getSpecificWorkingHoursById(workingHoursId);

      if (!existingWorkingHours) {
        console.log("Working hours not found for the given id");
        return res
          .status(404)
          .send(
            getErrorResponse(
              "NO_DATA_FOUND",
              "Working hours not found for the given id"
            )
          );
      }

      const invalidPayload: any = [];
      Object.entries(payload).forEach(([key, value]) => {
        const invalidData = validateTherapistTimeSlots(key, value);
        if (invalidData && invalidData.length) {
          invalidPayload.push(...invalidData);
        }
      });

      if (invalidPayload.length > 0) {
        console.log("Overlapping slots found");
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "Overlapping slots found",
              invalidPayload
            )
          );
      }

      // checking in google calender if the slots are available
      // const isTimeSlotBusy: any = await GoogleCalendarService.isTimeSlotBusy(
      //   therapistId,
      //   payload.date,
      //   payload.slots
      // );

      // if (!isTimeSlotBusy.status) {
      //   console.log(isTimeSlotBusy.message);
      //   return res
      //     .status(400)
      //     .send(getErrorResponse("VALIDATION_ERROR", isTimeSlotBusy.message));
      // }

      // if (isTimeSlotBusy.busy) {
      //   console.log(isTimeSlotBusy.message);
      //   return res
      //     .status(400)
      //     .send(
      //       getErrorResponse(
      //         "VALIDATION_ERROR",
      //         isTimeSlotBusy.message,
      //         isTimeSlotBusy.busySlots
      //       )
      //     );
      // }

      // saving the working hours
      const updatedSlots = await WorkingHoursService.updateSpecificWorkingHours(
        payload,
        therapistId
      );

      if (!updatedSlots) {
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Specific working hours update error"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Specific working hours updated successfully",
        data: updatedSlots,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async getTherapistSpecificWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.therapist._id;
      const date = req.query?.date;

      if (date) {
        if (typeof date !== "string") {
          return res
            .status(400)
            .send(
              getErrorResponse("VALIDATION_ERROR", "Date should be string type")
            );
        }

        const dateRegex = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/;
        if (!dateRegex.test(date)) {
          return res
            .status(400)
            .send(getErrorResponse("VALIDATION_ERROR", "Invalid date"));
        }
      }

      const getCondition: any = {
        therapistId,
      };

      // adding date parameter in condition if exists in payload
      if (date) {
        getCondition["date"] = date;
      }

      const workingHoursData: any =
        await WorkingHoursService.getTherapistSpecificWorkingHours(
          getCondition
        );

      if (!workingHoursData) {
        console.log("No Therapist specific working hours data found");
        return res
          .status(404)
          .send(
            getErrorResponse(
              "NO_DATA_FOUND",
              "No Therapist specific working hours data found"
            )
          );
      }

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Fetched Therapist specific working hours data successfully",
        data: workingHoursData,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async getAllTherapistWorkingHours(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const therapistId = req.params.id;

      // verifying if therapist exists with the id provided
      const existingTherapist = await TherapistService.getTherapist(
        therapistId
      );

      if (!existingTherapist) {
        console.log("Therapist not found for the given id");
        return res
          .status(404)
          .send(
            getErrorResponse(
              "NO_DATA_FOUND",
              "Therapist not found for the given id"
            )
          );
      }

      const sessionType = req.query.sessionType as string;

      if (!sessionType) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "sessionType query parameter is required"
            )
          );
      }

      // Validate session type
      if (!["introductory", "consultancy"].includes(sessionType)) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "sessionType must be either 'introductory' or 'consultancy'"
            )
          );
      }

      // Prepare duration condition based on session type
      const durationCondition =
        sessionType === "introductory"
          ? { "slots.duration": 15 } // For introductory sessions
          : { "slots.duration": { $ne: 15 } }; // For consultancy sessions

      // Get working hours from both collections
      const [regularWorkingHours, specificWorkingHours] = await Promise.all([
        WorkingHoursService.getTherapistAllWorkingHours(
          therapistId,
          durationCondition
        ),
        WorkingHoursService.getTherapistAllSpecificWorkingHours({
          therapistId,
          ...durationCondition,
        }),
      ]);

      // Get existing schedules
      const old_schedules = await ScheduleService.getScheduleByTherapistId(
        therapistId
      );

      let existing_dates: any = [];
      let conflicts: any = [];

      // Format existing schedules
      for (const old_schedule of old_schedules) {
        for (const old_date of old_schedule.recurrenceDates) {
          if (old_date.status != ScheduleStatus.CANCELLED) {
            existing_dates.push({
              fromDate: moment(old_date.fromDate),
              toDate: moment(old_date.toDate),
            });
          }
        }
      }

      // Check conflicts for regular working hours
      if (regularWorkingHours && regularWorkingHours[0]) {
        Object.keys(regularWorkingHours[0]).forEach((day: any) => {
          if (
            regularWorkingHours[0][day] &&
            Array.isArray(regularWorkingHours[0][day])
          ) {
            regularWorkingHours[0][day].forEach((slot: any) => {
              // For each existing date, check if time overlaps
              existing_dates.forEach((existingDate: any) => {
                const existingDay = moment(existingDate.fromDate)
                  .format("dddd")
                  .toLowerCase();

                // Only compare if the day matches
                if (existingDay === day) {
                  // Convert slot times to 24-hour format for comparison
                  const [slotStartHour, slotStartMin] =
                    slot.startTime.split(":");
                  const [slotEndHour, slotEndMin] = slot.endTime.split(":");
                  const [existingStartHour, existingStartMin] = moment(
                    existingDate.fromDate
                  )
                    .format("HH:mm")
                    .split(":");
                  const [existingEndHour, existingEndMin] = moment(
                    existingDate.toDate
                  )
                    .format("HH:mm")
                    .split(":");

                  // Convert all times to minutes since midnight
                  const slotStart =
                    parseInt(slotStartHour) * 60 + parseInt(slotStartMin);
                  const slotEnd =
                    parseInt(slotEndHour) * 60 + parseInt(slotEndMin);
                  const existingStart =
                    parseInt(existingStartHour) * 60 +
                    parseInt(existingStartMin);
                  const existingEnd =
                    parseInt(existingEndHour) * 60 + parseInt(existingEndMin);

                  // Check for overlap
                  if (slotStart < existingEnd && slotEnd > existingStart) {
                    conflicts.push({
                      type: "regular",
                      day: day,
                      conflictingSlot: {
                        startTime: slot.startTime,
                        endTime: slot.endTime,
                        duration: slot.duration,
                      },
                      existingSchedule: {
                        fromDate: moment(existingDate.fromDate).format(
                          "YYYY-MM-DD HH:mm"
                        ),
                        toDate: moment(existingDate.toDate).format(
                          "YYYY-MM-DD HH:mm"
                        ),
                      },
                    });
                  }
                }
              });
            });
          }
        });
      }

      // Check conflicts for specific working hours
      if (specificWorkingHours && specificWorkingHours.length > 0) {
        specificWorkingHours.forEach((workingHour) => {
          if (workingHour.slots && Array.isArray(workingHour.slots)) {
            workingHour.slots.forEach((slot: any) => {
              const slotStartDateTime = moment(
                `${workingHour.date} ${slot.startTime}`,
                "DD-MM-YYYY HH:mm"
              );
              const slotEndDateTime = moment(
                `${workingHour.date} ${slot.endTime}`,
                "DD-MM-YYYY HH:mm"
              );

              existing_dates.forEach((existingDate: any) => {
                if (
                  slotStartDateTime.isBefore(existingDate.toDate) &&
                  slotEndDateTime.isAfter(existingDate.fromDate)
                ) {
                  conflicts.push({
                    type: "specific",
                    date: workingHour.date,
                    conflictingSlot: {
                      startTime: slot.startTime,
                      endTime: slot.endTime,
                      duration: slot.duration,
                    },
                    existingSchedule: {
                      fromDate: moment(existingDate.fromDate).format(
                        "YYYY-MM-DD HH:mm"
                      ),
                      toDate: moment(existingDate.toDate).format(
                        "YYYY-MM-DD HH:mm"
                      ),
                    },
                  });
                }
              });
            });
          }
        });
      }

      // Combine and format the data
      const formattedData = {
        regularWorkingHours: regularWorkingHours || {},
        specificWorkingHours: specificWorkingHours || [],
        conflicts,
      };

      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: `Fetched ${sessionType} working hours successfully`,
        data: formattedData,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  static async sessionBooking(
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) {
    try {
      const payload = req.body;

      // validating if therapist exist and is verified
      const existingTherapist = await TherapistService.getTherapist(
        payload.therapistId
      );

      if (!existingTherapist) {
        return res
          .status(404)
          .send(
            getErrorResponse(
              "DATA_NOT_FOUND",
              "Therapist not found with the given id"
            )
          );
      }

      if (existingTherapist.isVerified === false) {
        return res
          .status(400)
          .send(
            getErrorResponse("VALIDATION_ERROR", "Therapist not verified.")
          );
      }

      let {
        therapistId,
        email,
        clientCountry,
        description,
        location,
        timezone,
        fromDate,
        toDate,
        name,
        phone,
        gender,
        age,
        amount,
        isBefore,
        sessionType,
      } = req.body;

      const emails = [email];
      const recurrence = "Does Not Repeat";
      const summary = `${existingTherapist.name}<>${payload.name}`;

      fromDate = moment(fromDate);
      toDate = moment(toDate);

      const duration = toDate.diff(fromDate, "minutes");

      if (duration <= 0) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "INVALID_REQUEST",
              "Select a valid Time for Appointment Duration"
            )
          );
      }

      // get and check for dates overlap
      let recurrenceDates: any = [
        {
          fromDate: fromDate,
          toDate: toDate,
          amount: amount,
          rrule: "",
        },
      ];

      const old_schedules = await ScheduleService.getScheduleByTherapistId(
        therapistId
      );
      // console.log({ old_schedules });
      let existing_dates = [];
      for (const old_schedule of old_schedules) {
        for (const old_date of old_schedule.recurrenceDates) {
          if (old_date.status != ScheduleStatus.CANCELLED) {
            existing_dates.push({
              fromDate: moment(old_date.fromDate),
              toDate: moment(old_date.toDate),
            });
          }
        }
      }

      const checkifOverlap = isOverlap(recurrenceDates, existing_dates);

      if (checkifOverlap) {
        return res
          .status(400)
          .send(
            getErrorResponse("VALIDATION_ERROR", "Appointment already exists")
          );
      }

      let isEvent = false;
      for (const recurrence of recurrenceDates) {
        const event = await GoogleCalendarService.eventByDate(
          therapistId,
          200,
          recurrence.fromDate,
          recurrence.toDate
        );

        if (event.length > 0) {
          isEvent = true;
          break;
        }
      }
      if (isEvent) {
        return res
          .status(400)
          .send(
            getErrorResponse(
              "VALIDATION_ERROR",
              "This session is already added to your Google Calendar. Please check your calendar to avoid duplicate entries."
            )
          );
      }

      // get or create client if not exist
      let client: any = undefined;
      for (let email of emails) {
        const clientExist = await ClientService.getClientByEmail(
          email,
          therapistId
        );
        if (clientExist) {
          client = clientExist;
          break;
        }
      }

      if (client) {
        if (client.isActive === false) {
          return res
            .status(400)
            .send(getErrorResponse("VALIDATION_ERROR", "Client is not active"));
        }
      }

      if (!client) {
        client = await ClientService.create({
          email: emails[0],
          name: name,
          therapistId: therapistId,
          phone: phone,
          gender: gender,
          age: age,
          defaultSessionAmount: String(amount),
          defaultTimezone: timezone,
          fromPublicCalender: true,
        });
        if (!client) {
          return res
            .status(500)
            .send(
              getErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "client not created. Something went wrong."
              )
            );
        }
      } else {
        client.name = name;
        client.phone = phone;
        client.gender = gender;
        client.age = age;
        await client.save();
      }

      // verifying if the client has any booking session earlier
      if (client) {
        const existingSchedule =
          await ScheduleDao.getLatestScheduleByTherapistAndClientId(
            therapistId,
            client._id
          );

        if (
          existingSchedule &&
          existingSchedule.recurrenceDates &&
          existingSchedule.recurrenceDates[0].amount
        ) {
          amount = existingSchedule.recurrenceDates[0].amount;
        }
      }

      const scheduleData: any = {
        clientId: client._id,
        additionalEmails: emails.filter(
          (email: string) => email != client.email
        ), // other clients
        duration: duration,
        clientCountry: clientCountry,
        recurrence: recurrence,
        // tillDate: tillDate,
        description: description,
        recurrenceDates: recurrenceDates,
        location: location,
        age: age,
        gender: gender,
        summary: summary,
        paymentIsBefore: isBefore,
        fromPublicCalender: true,
      };

      if (
        client &&
        (!client.fromPublicCalender || client.fromPublicCalender === false)
      ) {
        scheduleData.fromPublicCalender = false;
      }
      const schedule = await ScheduleService.create(
        therapistId,
        scheduleData,
        true
      );

      if (!schedule) {
        return res
          .status(500)
          .send(
            getErrorResponse(
              "INTERNAL_SERVER_ERROR",
              "Unable to create schedule."
            )
          );
      }

      client.defaultTimezone = timezone;
      await client.save();

      let recurrenceDate = schedule.recurrenceDates[0];

      const addEventPayload: any = {
        emails: emails,
        summary,
        location: location,
        description: description,
      };

      const recurranceData = {
        fromDate: recurrenceDate.fromDate,
        toDate: recurrenceDate.toDate,
        _id: recurrenceDate._id,
        rrule: recurrenceDates[0].rrule,
        data: schedule.recurrenceDates,
      };

      let googleCalenderEvent: GoogleCalendarEvent = {
        link: "",
        calenderEventId: [],
      };
      if (location === "online") {
        googleCalenderEvent = await GoogleCalendarService.addEventToCalender(
          therapistId,
          addEventPayload,
          recurranceData,
          schedule._id
        );
      }
      // const paymentLink = await StripeService.createPaymentLink(amount, req.therapist, schedule._id, client._id, recurrenceDate._id);
      let paymentLink: any;
      let payTrackerId = undefined;
      let updateRecurrenceDate: any = {};
      for (const event of googleCalenderEvent.calenderEventId) {
        if (existingTherapist.menus.paymentGateway) {
          if (!req.body.collectYourself) {
            paymentLink = await RazorpayService.createPaymentLink(
              amount,
              req.therapist._id,
              schedule._id,
              event.scheduleRecId,
              client._id
            );
            if (!paymentLink) {
              return res
                .status(500)
                .send(
                  getErrorResponse(
                    "INTERNAL_SERVER_ERROR",
                    "Unable to create payment link."
                  )
                );
            }
          }
        }
        if (existingTherapist.menus.paymentTracker) {
          const paymentTracker = await PayTrackerService.createPayTracker({
            therapistId: therapistId,
            scheduleId: event?.scheduleId,
            scheduleRecId: event.scheduleRecId,
            clientId: client._id,
            dueDate: event.start?.dateTime || "",
            amount: {
              currency: "INR",
              value: recurrenceDate.amount,
            },
            paymentType: isBefore
              ? PaymentTrackerTypeEnum.Advance
              : PaymentTrackerTypeEnum.Post_Session,
            status: PaymentTrackerStatusEnum.Still_Pending,
            paymentDate: undefined,
            isDeleted: false,
            tags: [],
            sendRemainder: 0,
            isFine: false,
            cancellationFee: {
              currency: "INR",
              value: 0,
            },
            minFee: existingTherapist.minFee,
            maxFee: existingTherapist.maxFee,
          });

          payTrackerId = paymentTracker._id;
        }
        updateRecurrenceDate = await ScheduleService.updateRecurrenceDate(
          event?.scheduleId || "",
          event?.scheduleRecId || "",
          paymentLink?.transactionId || undefined,
          googleCalenderEvent?.link || "",
          event?._id || "",
          payTrackerId
        );
        if (!updateRecurrenceDate) {
          return res
            .status(500)
            .send(
              getErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "Unable to update recurrence date."
              )
            );
        }
      }

      const scheduleAfterCreated: any =
        await ScheduleService.getScheduleByTherapistAndClientId(
          therapistId,
          scheduleData.clientId
        );
      const recentRecurrenceDate: any = await Utility.findNearestFromDate(
        updateRecurrenceDate.recurrenceDates
      );
      // let paymentLink: any;
      for (let email of emails) {
        let receiverData: any;
        const client = await ClientService.getClientByEmail(email, therapistId);
        if (!client) {
          continue;
        } else {
          receiverData = emails.map((email: any) => {
            return {
              email: email,
              name: client.name || "There",
            };
          });
        }

        let senderData = {
          email: CONFIG.companyEmail,
          name: CONFIG.companyName,
        };

        // sending email to client
        // let subject = scheduleData.summary;
        let subject = MailSubjectEnum.REMAINDER;
        let clientHtmlTemplate;
        const clientEmailParams: any = {
          clientName: client.name || "There",
          therapistName: existingTherapist.name,
          scheduleDate: recentRecurrenceDate.fromDate,
          meetingLink: recentRecurrenceDate.meetLink,
          // paymentLink: paymentLink?.paymentLink,
          // payLater: false,
          timezone: client.defaultTimezone,
          amount: amount,
        };

        if (sessionType === "introductory") {
          clientHtmlTemplate =
            introScheduleReminderForClient(clientEmailParams);
        } else {
          clientEmailParams["bookingMessage"] =
            existingTherapist.bookingMessage;
          clientHtmlTemplate =
            sessionScheduleReminderForClient(clientEmailParams);
        }

        const sentEmailToClient = await Mailer2.sendMail(
          senderData,
          receiverData,
          subject,
          clientHtmlTemplate
        );
        if (!sentEmailToClient) {
          return res
            .status(500)
            .send(
              getErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "mail sending failed for client"
              )
            );
        }

        // sending email to therapist
        const therapistMailData = [
          {
            email: existingTherapist.email,
            name: existingTherapist.name,
          },
        ];

        let htmlTemplateForTherapist;
        const therapistEmailParams: any = {
          clientName: client.name || "There",
          therapistName: existingTherapist.name,
          scheduleDate: recentRecurrenceDate.fromDate,
          meetingLink: recentRecurrenceDate.meetLink,
          // paymentLink: paymentLink?.paymentLink,
          // payLater: false,
          timezone: client.defaultTimezone,
          amount: amount,
          gender: payload.gender,
          age: payload.age,
          clientEmail: payload.email,
        };

        if (sessionType === "introductory") {
          htmlTemplateForTherapist =
            introScheduleReminderForTherapist(therapistEmailParams);
        } else {
          htmlTemplateForTherapist =
            sessionScheduleReminderForTherapist(therapistEmailParams);
        }

        const sentEmailToTherapist = await Mailer2.sendMail(
          senderData,
          therapistMailData,
          subject,
          htmlTemplateForTherapist
        );
        if (!sentEmailToTherapist) {
          return res
            .status(500)
            .send(
              getErrorResponse(
                "INTERNAL_SERVER_ERROR",
                "mail sending failed for therapist"
              )
            );
        }
        // Convert to client's timezone for WhatsApp notification
        const clientTimezone = client?.defaultTimezone || "Asia/Kolkata";
        const formattedDate = moment(recentRecurrenceDate.fromDate)
          .tz(clientTimezone)
          .format("DD-MM-YYYY");
        const formattedTime = moment(recentRecurrenceDate.fromDate)
          .tz(clientTimezone)
          .format("HH:mm");

        // sending whatsapp message to client
        const clientParams = {
          CLIENT_NAME: client.name,
          THERAPIST_NAME: existingTherapist.name,
          SESSION_DATE: formattedDate,
          SESSION_TIME: formattedTime,
          MEETLINK: recentRecurrenceDate.meetLink,
          BOOKING_MESSAGE: existingTherapist.bookingMessage,
        };

        let clientTemp;
        if (sessionType === "introductory") {
          clientTemp = "client_intro_session";
        } else {
          clientTemp = "client_consultancy_session";
        }
        await sendWhatsAppNotification(client, clientTemp, clientParams);

        // sending whatsapp message to therapist
        const therapistParams = {
          CLIENT_NAME: client.name,
          THERAPIST_NAME: existingTherapist.name,
          SESSION_DATE: formattedDate,
          SESSION_TIME: formattedTime,
          MEETLINK: recentRecurrenceDate.meetLink,
          GENDER: payload.gender,
          AGE: payload.age,
          EMAIL_ID: payload.email,
        };

        let therapistTemp;
        if (sessionType === "introductory") {
          therapistTemp = "therapist_intro_session";
        } else {
          therapistTemp = "therapist_consultancy_session";
        }
        await sendWhatsAppNotification(
          existingTherapist,
          therapistTemp,
          therapistParams
        );
      }
      return res.status(200).json({
        responseCode: 0,
        status: "success",
        message: "Booked session successfully",
        data: {},
      });
    } catch (error) {
      next(error);
    }
  }
}
