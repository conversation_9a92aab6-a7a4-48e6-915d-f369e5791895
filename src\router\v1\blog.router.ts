import { Router } from "express";
import BlogController from "../../controller/blog.controller";
import { authMiddleware } from "../../middleware/AuthMiddleware";
import { adminAuthMiddleware } from "../../middleware/admin.auth.middleware";
import { upload } from "../../helper/fileUpload";

export default class BlogRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    // GET
    // this.router.get("/", BlogController.getAllBlogs);
    // for blogs listing on website
    this.router.get("/visible", BlogController.getVisibleBlogs);
    // for blog detail page on website
    // this.router.get("/:id", BlogController.getBlogById);
    this.router.get("/handle/:handle", BlogController.getBlogByHandle); // public

    // POST
    // this.router.post(
    //   "/",
    //   //   authMiddleware(),
    //   BlogController.createBlog
    // );

    // Image upload routes
    // this.router.post(
    //   "/upload/blog-image",
    //   //   authMiddleware(),
    //   upload.single("image"),
    //   BlogController.uploadBlogImage
    // );

    // this.router.post(
    //   "/upload/blog-image-mobile",
    //   //   authMiddleware(),
    //   upload.single("image"),
    //   BlogController.uploadBlogImageMobile
    // );

    // this.router.post(
    //   "/upload/writer-image",
    //   //   authMiddleware(),
    //   upload.single("image"),
    //   BlogController.uploadWriterImage
    // );

    // PUT
    // this.router.put(
    //   "/:id",
    //   //  authMiddleware(),
    //   BlogController.updateBlog
    // );
    // this.router.put(
    //   "/handle/:handle",
    //   //   authMiddleware(),
    //   BlogController.updateBlogByHandle
    // );

    // DELETE
    // this.router.delete("/:id", BlogController.deleteBlog);
    // this.router.delete(
    //   "/handle/:handle",
    //   //   authMiddleware(),
    //   BlogController.deleteBlogByHandle
    // );

    // Image delete route
    // this.router.delete(
    //   "/image/delete",
    //   //   authMiddleware(),
    //   BlogController.deleteBlogImage
    // );


  }
}
