import { BrevoWhatsapp } from "../util/brevoWhatsapp";
import { BrevoService } from "../services/brevo.service";

export async function sendWhatsAppNotification(
  client: any,
  searchingWord: string,
  params: any
) {
  try {
    // Fetch WhatsApp templates
    const templateListResponse = await BrevoService.sendRequest(
      "/v3/whatsappCampaigns/template-list",
      "get"
    );

    if (!templateListResponse.templates?.length) return;

    // Find the template matching the keyword
    const matchingTemplate = templateListResponse.templates.find(
      (template: any) =>
        template.name.toLowerCase().includes(searchingWord.toLowerCase())
    );
    if (!matchingTemplate) return;

    if (!client?.phone || typeof client.phone !== "string") return;

    // Format phone number
    const formattedPhone = `+91${client.phone}`;

    // Send WhatsApp message
    await BrevoWhatsapp.sendWhatsAppMessage(
      [{ phone: formattedPhone }],
      matchingTemplate.id,
      params
    );
  } catch {
    return null; // Prevents crashes on error
  }
}
