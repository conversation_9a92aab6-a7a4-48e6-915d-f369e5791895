# Google Calendar Service Logging Implementation

## Overview

This document describes the comprehensive logging implementation added to the Google Calendar service functions to help identify and debug issues with calendar event creation, particularly when events are created but not showing on the calendar.

## Enhanced Functions with Logging

### 1. `addEventToCalender` - Main Calendar Event Creation Function

This function now includes detailed logging at every step:

- **Initial Setup**: Logs therapist and calendar data retrieval
- **OAuth Configuration**: Logs token setup and authentication
- **Event Preparation**: Logs attendee preparation and event data
- **Google Calendar API Calls**: Logs all API interactions
- **Recurring Event Handling**: Detailed logging for complex recurring event scenarios
- **Error Handling**: Comprehensive error logging with context

**Key Log Points:**
```typescript
// Function entry
logger.info('Starting addEventToCalender', logContext);

// OAuth setup
logger.info('OAuth2 credentials set successfully', logContext);

// Event creation
logger.info('Successfully created event in Google Calendar', {
  createdEventId: createdEvent.data.id,
  hangoutLink: createdEvent.data.hangoutLink,
  eventStatus: createdEvent.data.status
});

// Error scenarios
logger.error('Error during OAuth setup or event creation', {
  error: error?.message,
  stack: error?.stack
});
```

### 2. `addSingleEventToCalender` - Single Event Creation

Enhanced with similar logging patterns as the main function but optimized for single event scenarios.

### 3. `updateEventDescriptions` - Event Description Updates

Logs the process of updating event descriptions with tracking markers:

```typescript
logger.info('Successfully updated event descriptions', {
  parentEventId,
  updatedDescriptionLength: updatedDescription.length
});
```

### 4. `createSchedule` - New Comprehensive Schedule Creation Function

A new wrapper function that combines schedule creation with calendar event creation:

```typescript
const result = await GoogleCalendarService.createSchedule(
  therapistId,
  scheduleData,
  eventData,
  recurrenceData
);
```

This function provides:
- End-to-end logging of the entire schedule creation process
- Separate error handling for database vs. calendar operations
- Detailed success/failure tracking

## Log Context Structure

Each function uses a consistent log context structure:

```typescript
const logContext = {
  therapistId,
  scheduleId,
  function: 'functionName',
  recurrenceDate: {
    fromDate: recurrenceDate.fromDate,
    toDate: recurrenceDate.toDate,
    _id: recurrenceDate._id,
    hasRrule: !!recurrenceDate.rrule,
    dataLength: recurrenceDate.data?.length || 0
  },
  eventData: {
    summary: data.summary,
    location: data.location,
    emailCount: data.emails?.length || 0
  }
};
```

## Database Logging

In addition to Winston file logging, critical events are also saved to the database using the existing `apiLogModel`:

```typescript
await GoogleCalendarService.saveApiLog(
  therapistId,
  "addEventToCalender_success",
  JSON.stringify({ 
    success: true,
    eventCount: finalLinkData.calenderEventId.length,
    hasHangoutLink: !!finalLinkData.link
  })
);
```

## Log Files

Logs are written to:
- `logs/app.log` - All logs (info level and above)
- `logs/error.log` - Error logs only
- Console output with colors for development

## Error Categories

The logging system categorizes errors into specific types:

1. **Setup Errors**: `addEventToCalender_setup_error`
2. **OAuth Errors**: `addEventToCalender_oauth_error`
3. **Recurring Event Errors**: `addEventToCalender_recurring_error`
4. **Authentication Errors**: `addEventToCalender_auth_error`
5. **Success Events**: `addEventToCalender_success`

## Debugging Third-Party Calendar Issues

To identify when events are created but not showing on calendar:

1. **Check Success Logs**: Look for `addEventToCalender_success` entries
2. **Verify Event IDs**: Check if `createdEventId` is present in success logs
3. **Check Hangout Links**: Verify `hasHangoutLink: true` in logs
4. **Monitor Recurring Events**: Look for `recurring_event_instances` errors
5. **API Response Tracking**: Check `eventStatus` in logs (should be 'confirmed')

## Usage Examples

### Basic Schedule Creation with Logging
```typescript
try {
  const result = await GoogleCalendarService.createSchedule(
    therapistId,
    scheduleData,
    eventData,
    recurrenceData
  );
  
  console.log('Schedule created successfully:', result.schedule._id);
  if (result.calendarResult) {
    console.log('Calendar events created:', result.calendarResult.calenderEventId.length);
  }
} catch (error) {
  console.error('Schedule creation failed:', error.message);
  // Check logs for detailed error information
}
```

### Monitoring Logs for Issues
```bash
# Watch for errors in real-time
tail -f logs/error.log | grep "addEventToCalender"

# Check for specific therapist issues
grep "therapistId.*12345" logs/app.log | grep "ERROR"

# Monitor successful calendar creations
grep "addEventToCalender_success" logs/app.log
```

## Troubleshooting Common Issues

1. **Events Created But Not Visible**: Check for `eventStatus` != 'confirmed' in logs
2. **Recurring Event Problems**: Look for `recurring_event_instances` errors
3. **Authentication Issues**: Monitor `oauth_error` and `auth_error` logs
4. **Third-Party API Issues**: Check for Google Calendar API error responses in logs

This comprehensive logging system will help identify exactly where and why calendar events might be failing to appear properly in Google Calendar.
